using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using QuantConnect;
using QuantConnect.Algorithm;
using QuantConnect.Brokerages;
using QuantConnect.Orders;
using QuantConnect.Securities;
using QuantConnect.Algorithm.CSharp;

namespace cCryptoBot {
  public class Testing {
    private readonly cCryptoBotAlgorithm _algorithm;
    private readonly Symbol _symbol;
    SymbolProperties _symbolProperties = null!;
    private bool _isTesting = false;
    public Testing(cCryptoBotAlgorithm algorithm, Symbol symbol, SymbolProperties symbolProperties) {
      _algorithm = algorithm;
      _symbol = symbol;
      _symbolProperties = symbolProperties;
    }

    public void PlaceTestOrders() {
      try {
        _isTesting = true;
        var currentPrice = _algorithm.Securities[_symbol].Price;
        if (currentPrice <= 0) {
          _algorithm.Log("Could not get valid current price for test orders. Skipping.");
          return;
        }

        const int STOP_LOSS_COUNT = 1;
        const int LIMIT_ORDER_COUNT = 1;

        var pricePrecision = Math.Max(0, (int)-Math.Log10((double)_symbolProperties.MinimumPriceVariation));

        // Place STOP LIMIT orders (Sell orders below current price)
        var stopBasePrice = currentPrice * 0.9m;
        _algorithm.Log($"Placing {STOP_LOSS_COUNT} test StopLimit sell orders starting below {stopBasePrice.ToString($"F{pricePrecision}")}");
        for (int i = 0; i < STOP_LOSS_COUNT; i++) {
          var stopPrice = Math.Round(stopBasePrice - (i * _symbolProperties.MinimumPriceVariation), pricePrecision);
          var limitPrice = stopPrice;
          var quantity = -_symbolProperties.LotSize * 10; // Negative for sell orders
          var reason = $"Test StopLimit order {i + 1}/{STOP_LOSS_COUNT}";

          _algorithm.EnqueueTestOrder("StopLossClosePosition", i, 0, quantity, limitPrice, true, reason);
        }

        // Place LIMIT orders (Sell orders above current price)
        var limitBasePrice = currentPrice * 1.13m;
        _algorithm.Log($"Placing {LIMIT_ORDER_COUNT} test Limit sell orders starting above {limitBasePrice.ToString($"F{pricePrecision}")}");
        for (int i = 0; i < LIMIT_ORDER_COUNT; i++) {
          var limitPrice = Math.Round(limitBasePrice + (i * _symbolProperties.MinimumPriceVariation), pricePrecision);

          if (limitPrice <= 0) {
            _algorithm.Log($"Skipping test Limit order {i+1} due to non-positive price: limit={limitPrice}");
            continue;
          }

          var quantity = -_symbolProperties.LotSize * 10; // Negative for sell orders
          var reason = $"Test Limit order {i + 1}/{LIMIT_ORDER_COUNT}";

          _algorithm.EnqueueTestOrder("TakeProfitClosePosition", i, 0, quantity, limitPrice, false, reason);
        }

        _algorithm.Log($"Finished enqueueing {STOP_LOSS_COUNT + LIMIT_ORDER_COUNT} test orders.");
      } catch (Exception ex) {
        _algorithm.Log($"Error placing test orders: {ex.Message}");
      }
    }

    public bool IsTesting => _isTesting;

    public void ScheduleCancelAllOrdersAfter_1_Minutes() {
      try {
        _algorithm.Log("Scheduling cancellation of all orders after 1 minutes...");
        var cancelTime = DateTime.UtcNow.AddMinutes(1);
        _algorithm.Schedule.On(_algorithm.DateRules.On(cancelTime.Date),
                               _algorithm.TimeRules.At(cancelTime.Hour, cancelTime.Minute),
                               CancelAllOrders);

        _algorithm.Log($"Scheduled cancellation of all orders at {cancelTime:yyyy-MM-dd HH:mm:ss} UTC");
      } catch (Exception ex) {
        _algorithm.Log($"Error scheduling order cancellation: {ex.Message}");
      }
    }

    public void CancelAllOrders() {
      try {
        _algorithm.Log("Executing scheduled cancellation of all orders...");

        var startTime = DateTime.UtcNow;

        var method = _algorithm.GetType().GetMethod("EnqueueCancelAllOrders", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (method != null) {
          method.Invoke(_algorithm, new object[] { "Scheduled test cancellation after 15 minutes" });
          var elapsedTimeMs = (DateTime.UtcNow - startTime).TotalMilliseconds;
        } else {
          _algorithm.Log("Error: Could not find EnqueueCancelAllOrders method");
        }
      } catch (Exception ex) {
        _algorithm.Log($"Error cancelling all orders: {ex.Message}");
      }
    }
  }
}
